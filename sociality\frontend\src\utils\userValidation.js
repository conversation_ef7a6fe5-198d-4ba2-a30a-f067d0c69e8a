/**
 * Utility functions for validating user data
 */

/**
 * Validates if a user object has all required fields and valid data
 * @param {Object} user - The user object to validate
 * @returns {boolean} - True if user is valid, false otherwise
 */
export const isValidUser = (user) => {
  return (
    user &&
    user._id &&
    user.username &&
    user.name &&
    typeof user.username === 'string' &&
    typeof user.name === 'string' &&
    user.username.trim() !== '' &&
    user.name.trim() !== ''
  );
};

/**
 * Filters an array of users to only include valid users
 * @param {Array} users - Array of user objects
 * @returns {Array} - Array of valid user objects
 */
export const filterValidUsers = (users) => {
  if (!Array.isArray(users)) {
    return [];
  }
  
  return users.filter(isValidUser);
};

/**
 * Validates and sanitizes user data
 * @param {Object} user - The user object to sanitize
 * @returns {Object|null} - Sanitized user object or null if invalid
 */
export const sanitizeUser = (user) => {
  if (!isValidUser(user)) {
    return null;
  }

  return {
    _id: user._id,
    username: user.username.trim(),
    name: user.name.trim(),
    profilePic: user.profilePic || '',
    bio: user.bio || '',
    followers: user.followers || [],
    following: user.following || [],
    // Add other safe fields as needed
  };
};
